// Test script to verify API connectivity
const axios = require('axios');

const API_URL = 'http://127.0.0.1:8000/api';

async function testAPI() {
  console.log('Testing API connectivity...');
  console.log('API URL:', API_URL);

  try {
    // Test basic connectivity
    console.log('\n1. Testing basic connectivity...');
    const response = await axios.get(`${API_URL}/test`, {
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500; // Accept any status code less than 500
      }
    });
    
    console.log('Status:', response.status);
    console.log('Response:', response.data);

    // Test login endpoint
    console.log('\n2. Testing login endpoint...');
    const loginResponse = await axios.post(`${API_URL}/login`, {
      email: '<EMAIL>',
      password: 'password'
    }, {
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    console.log('Login Status:', loginResponse.status);
    console.log('Login Response:', loginResponse.data);

    // Test notification endpoint
    console.log('\n3. Testing notification endpoint...');
    const notificationResponse = await axios.get(`${API_URL}/notification`, {
      timeout: 5000,
      validateStatus: function (status) {
        return status < 500;
      }
    });
    
    console.log('Notification Status:', notificationResponse.status);
    console.log('Notification Response:', notificationResponse.data);

  } catch (error) {
    console.error('API Test Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
  }
}

testAPI();
