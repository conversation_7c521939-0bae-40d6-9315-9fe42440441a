import api from "./api";
import { API_URL } from "../config/api";

/**
 * Service pour gérer les interactions avec l'API Quiz
 * Reproduit exactement les fonctionnalités du frontend web
 */
export const QuizService = {
  /**
   * Récupère tous les quiz pour un cours donné
   * @param {string} token - Token d'authentification
   * @param {number} courseId - ID du cours
   * @returns {Promise<Array>} - Liste des quiz
   */
  getQuizzesByCourse: async (token, courseId) => {
    try {
      console.log(`Mobile: Calling API: ${API_URL}/quiz?cours=${courseId}`);

      // Le token sera automatiquement ajouté par l'intercepteur API
      const response = await api.get(`/quiz?cours=${courseId}`);

      if (!response.data) {
        throw new Error("Aucune donnée reçue du serveur");
      }

      // Gérer les différents formats de réponse (identique au frontend web)
      let quizzes = [];
      if (response.data["hydra:member"]) {
        quizzes = response.data["hydra:member"];
      } else if (Array.isArray(response.data)) {
        quizzes = response.data;
      } else if (response.data.quizzes) {
        quizzes = response.data.quizzes;
      }

      console.log(
        `Mobile: Found ${quizzes.length} quizzes for course ${courseId}`
      );
      return quizzes;
    } catch (error) {
      console.error("Mobile: Error fetching quizzes by course:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      if (error.response?.status === 404) {
        console.warn(`Mobile: No quizzes found for course ${courseId}`);
        return [];
      }

      throw new Error("Impossible de récupérer les quiz du cours");
    }
  },

  /**
   * Récupère un quiz par son IDModule
   * @param {string} token - Token d'authentification
   * @param {string} idModule - IDModule du quiz
   * @returns {Promise<Object>} - Données du quiz
   */
  getQuizByIdModule: async (token, idModule) => {
    try {
      if (!idModule) {
        throw new Error("IDModule is required");
      }

      console.log(`Mobile: Fetching quiz with IDModule: ${idModule}`);

      const response = await api.get(`/quiz/${encodeURIComponent(idModule)}`);

      if (!response.data) {
        throw new Error("Aucune donnée reçue du serveur");
      }

      console.log(
        `Mobile: Quiz data received for IDModule ${idModule}:`,
        response.data
      );
      return response.data;
    } catch (error) {
      console.error("Mobile: Error fetching quiz by IDModule:", error);

      if (error.response?.status === 404) {
        console.warn(
          `Mobile: Quiz not found for IDModule ${idModule}, creating demo quiz`
        );
        return QuizService.createDemoQuiz(idModule);
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      // Retourner un quiz fictif en cas d'erreur pour éviter de bloquer l'affichage
      console.warn(`Mobile: Fallback to demo quiz for IDModule ${idModule}`);
      return QuizService.createDemoQuiz(idModule || "ERROR-QUIZ");
    }
  },

  /**
   * Crée un quiz de démonstration
   * @param {string} idModule - IDModule du quiz
   * @returns {Object} - Quiz de démonstration
   */
  createDemoQuiz: (idModule) => {
    return {
      id: Math.floor(Math.random() * 1000),
      IDModule: idModule,
      Nom_FR: "Quiz de démonstration",
      Nom_EN: "Demo Quiz",
      Category: "Demo",
      Type: "Evaluation",
      MainSurface: false,
      Main: 0,
      Surface: 0,
      questions: [
        {
          id: 1,
          question: "Question de démonstration 1",
          answers: ["Réponse A", "Réponse B", "Réponse C", "Réponse D"],
          correctAnswer: 0,
        },
        {
          id: 2,
          question: "Question de démonstration 2",
          answers: ["Option 1", "Option 2", "Option 3", "Option 4"],
          correctAnswer: 1,
        },
      ],
    };
  },

  /**
   * Récupère la progression d'un apprenant pour un cours spécifique
   * @param {string} token - Token d'authentification
   * @param {number} apprenantId - ID de l'apprenant
   * @param {number} coursId - ID du cours
   * @returns {Promise<Object>} - Données de progression pour le cours
   */
  getProgressionByApprenantAndCours: async (token, apprenantId, coursId) => {
    try {
      if (!apprenantId || !coursId) {
        throw new Error("apprenantId and coursId are required");
      }

      console.log(
        `DEBUG: Récupération de la progression - Apprenant ID: ${apprenantId}, Cours ID: ${coursId}`
      );

      const response = await api.get(
        `/progression/apprenant/${apprenantId}/cours/${coursId}`
      );

      // Gérer les différents codes d'erreur HTTP
      if (!response.data) {
        console.warn(
          `No progression found for apprenant ${apprenantId} and cours ${coursId}`
        );
        // Initialiser une nouvelle progression plutôt que de retourner des données fictives
        return {
          progress_percentage: 0,
          quizzes_total: 0,
          quizzes_passed: 0,
          quiz_evaluations: [],
          is_completed: false,
          certificat: null,
        };
      }

      const result = response.data;
      console.log("DEBUG: Données de progression reçues:", result);

      return result;
    } catch (error) {
      console.error("Error fetching progression:", error);

      if (error.response?.status === 404) {
        console.warn(
          `No progression found for apprenant ${apprenantId} and cours ${coursId}`
        );
        return {
          progress_percentage: 0,
          quizzes_total: 0,
          quizzes_passed: 0,
          quiz_evaluations: [],
          is_completed: false,
          certificat: null,
        };
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      // Retourner des données par défaut en cas d'erreur
      return {
        progress_percentage: 0,
        quizzes_total: 0,
        quizzes_passed: 0,
        quiz_evaluations: [],
        is_completed: false,
        certificat: null,
      };
    }
  },

  /**
   * Récupère les certificats d'un apprenant
   * @param {string} token - Token d'authentification
   * @param {number} apprenantId - ID de l'apprenant
   * @returns {Promise<Array>} - Liste des certificats
   */
  getCertificatsByApprenant: async (token, apprenantId) => {
    try {
      if (!apprenantId) {
        throw new Error("apprenantId is required");
      }

      console.log(`Fetching certificats for apprenant ${apprenantId}`);

      const response = await api.get(`/certificat/apprenant/${apprenantId}`);

      if (!response.data) {
        return [];
      }

      const certificats = response.data.certificats || response.data || [];
      console.log(
        `Found ${certificats.length} certificats for apprenant ${apprenantId}`
      );

      return Array.isArray(certificats) ? certificats : [];
    } catch (error) {
      console.error("Error fetching certificats:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      return [];
    }
  },

  /**
   * Crée une évaluation pour un quiz
   * @param {string} token - Token d'authentification
   * @param {Object} evaluationData - Données de l'évaluation (quizId, apprenantId, statut)
   * @returns {Promise<Object>} - Réponse de l'API
   */
  createEvaluation: async (token, evaluationData) => {
    try {
      if (
        !evaluationData.quizId ||
        !evaluationData.apprenantId ||
        !evaluationData.statut
      ) {
        throw new Error("quizId, apprenantId and statut are required");
      }

      console.log("DEBUG: Création d'une évaluation avec les données:", {
        quizId: evaluationData.quizId,
        apprenantId: evaluationData.apprenantId,
        statut: evaluationData.statut,
        idmodule: evaluationData.idmodule,
      });

      const response = await api.post("/evaluation", evaluationData);

      console.log("DEBUG: Réponse de création d'évaluation:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating evaluation:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      throw new Error("Impossible de créer l'évaluation");
    }
  },

  /**
   * Récupère les compétences d'un quiz par IDModule (identique au frontend web)
   * @param {string} token - Token d'authentification
   * @param {string} quizIdOrModule - ID du quiz ou IDModule
   * @param {boolean} isIdModule - Si true, utilise IDModule, sinon utilise l'ID du quiz
   * @returns {Promise<Array>} - Liste des compétences
   */
  getCompetencesByQuiz: async (token, quizIdOrModule, isIdModule = true) => {
    try {
      if (!quizIdOrModule) {
        throw new Error("Quiz ID or IDModule is required");
      }

      console.log(
        `Mobile: Fetching competences for ${
          isIdModule ? "IDModule" : "quiz ID"
        }: ${quizIdOrModule}`
      );

      const paramName = isIdModule ? "idmodule" : "quiz";
      const response = await api.get(
        `/competence?${paramName}=${encodeURIComponent(quizIdOrModule)}`
      );

      if (!response.data) {
        return [];
      }

      const competences = response.data["hydra:member"] || response.data || [];
      console.log(
        `Mobile: Found ${competences.length} competences for ${quizIdOrModule}`
      );

      return competences;
    } catch (error) {
      console.error("Mobile: Error fetching competences:", error);

      if (error.response?.status === 404) {
        console.warn(`Mobile: No competences found for ${quizIdOrModule}`);
        return [];
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      return [];
    }
  },

  /**
   * Récupère les actions d'un quiz par IDModule (identique au frontend web)
   * @param {string} token - Token d'authentification
   * @param {string} idModule - IDModule du quiz
   * @returns {Promise<Array>} - Liste des actions
   */
  getActionsByIdModule: async (token, idModule) => {
    try {
      if (!idModule) {
        throw new Error("IDModule is required");
      }

      console.log(`Mobile: Fetching actions for IDModule: ${idModule}`);

      const response = await api.get(
        `/actions?idmodule=${encodeURIComponent(idModule)}`
      );

      if (!response.data) {
        return [];
      }

      const actions = response.data["hydra:member"] || response.data || [];
      console.log(
        `Mobile: Found ${actions.length} actions for IDModule ${idModule}`
      );

      return actions;
    } catch (error) {
      console.error("Mobile: Error fetching actions:", error);

      if (error.response?.status === 404) {
        console.warn(`Mobile: No actions found for IDModule ${idModule}`);
        return [];
      }

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      return [];
    }
  },

  /**
   * Génère un certificat pour un cours (identique au frontend web)
   * @param {string} token - Token d'authentification
   * @param {number} apprenantId - ID de l'apprenant
   * @param {number} coursId - ID du cours
   * @returns {Promise<Object>} - Données du certificat généré
   */
  generateCertificat: async (token, apprenantId, coursId) => {
    try {
      console.log(
        `Mobile: Generating certificate for apprenant ${apprenantId} and course ${coursId}`
      );

      const response = await api.post("/certificat/generate", {
        apprenantId: parseInt(apprenantId),
        coursId: parseInt(coursId),
      });

      console.log("Mobile: Certificate generation response:", response.data);

      return response.data;
    } catch (error) {
      console.error("Mobile: Error generating certificate:", error);

      if (error.response?.status === 401) {
        throw new Error("Session expirée, veuillez vous reconnecter");
      }

      if (error.response?.status === 400) {
        throw new Error("Données invalides pour la génération du certificat");
      }

      throw new Error("Impossible de générer le certificat");
    }
  },
};

export default QuizService;
