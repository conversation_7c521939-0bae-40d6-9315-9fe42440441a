<?php

/**
 * Script de test simple pour diagnostiquer les problèmes d'API
 */

$baseUrl = 'https://127.0.0.1:8000/api';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_VERBOSE, true);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $defaultHeaders = ['Content-Type: application/json'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($defaultHeaders, $headers));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

echo "=== Diagnostic des problèmes d'API ===\n\n";

// Test 1: Vérifier si le serveur répond
echo "1. Test de connectivité du serveur...\n";
$testResult = makeRequest($baseUrl . '/test');
echo "Status: " . $testResult['http_code'] . "\n";
echo "Réponse: " . $testResult['response'] . "\n";

if ($testResult['http_code'] === 401) {
    echo "✅ Serveur accessible (401 attendu pour /test sans auth)\n\n";
} else {
    echo "❌ Problème de connectivité serveur\n\n";
    exit(1);
}

// Test 2: Essayer de se connecter avec différents utilisateurs
echo "2. Test de connexion avec différents utilisateurs...\n";

$users = [
    ['email' => '<EMAIL>', 'password' => 'lyna123'],
    ['email' => '<EMAIL>', 'password' => 'tansim123'],
    ['email' => '<EMAIL>', 'password' => 'admin123']
];

$validToken = null;
$validUserId = null;

foreach ($users as $user) {
    echo "Tentative avec: " . $user['email'] . "\n";
    $loginResult = makeRequest($baseUrl . '/login', 'POST', $user);
    
    if ($loginResult['http_code'] === 200) {
        $loginData = json_decode($loginResult['response'], true);
        if (isset($loginData['token'])) {
            echo "✅ Connexion réussie!\n";
            $validToken = $loginData['token'];
            $validUserId = $loginData['user']['id'] ?? null;
            break;
        }
    } else {
        echo "❌ Échec: " . $loginResult['http_code'] . " - " . $loginResult['response'] . "\n";
    }
}

if (!$validToken) {
    echo "\n❌ Aucune connexion réussie. Impossible de continuer les tests.\n";
    exit(1);
}

echo "\n✅ Token obtenu. Utilisateur ID: $validUserId\n\n";

// Test 3: Test de l'endpoint de notification
echo "3. Test de l'endpoint de notification...\n";
$authHeaders = ["Authorization: Bearer $validToken"];

$notificationResult = makeRequest($baseUrl . '/notification', 'GET', null, $authHeaders);
echo "Status: " . $notificationResult['http_code'] . "\n";

if ($notificationResult['error']) {
    echo "❌ Erreur cURL: " . $notificationResult['error'] . "\n";
    echo "Ceci correspond à l'erreur ERR_NETWORK_IO_SUSPENDED\n";
} else {
    echo "✅ Endpoint notification accessible\n";
    echo "Réponse (100 premiers caractères): " . substr($notificationResult['response'], 0, 100) . "...\n";
}

// Test 4: Test de l'endpoint de progression
echo "\n4. Test de l'endpoint de progression...\n";

// Utiliser l'ID de l'utilisateur connecté et un cours existant
$apprenantId = $validUserId;
$coursId = 31; // Cours existant selon l'erreur

$progressionResult = makeRequest(
    $baseUrl . "/progression/apprenant/$apprenantId/cours/$coursId", 
    'GET', 
    null, 
    $authHeaders
);

echo "Status: " . $progressionResult['http_code'] . "\n";

if ($progressionResult['error']) {
    echo "❌ Erreur cURL: " . $progressionResult['error'] . "\n";
} else {
    if ($progressionResult['http_code'] === 404) {
        echo "⚠️  404 - Progression non trouvée (correspond à l'erreur signalée)\n";
        echo "Réponse: " . $progressionResult['response'] . "\n";
        
        // Vérifier si l'apprenant et le cours existent
        echo "\nVérification de l'existence de l'apprenant et du cours...\n";
        
        // Test avec un diagnostic
        $diagResult = makeRequest(
            $baseUrl . "/diagnostic/check-progression/$apprenantId/$coursId",
            'GET',
            null,
            $authHeaders
        );
        
        echo "Diagnostic Status: " . $diagResult['http_code'] . "\n";
        echo "Diagnostic Réponse: " . $diagResult['response'] . "\n";
        
    } else {
        echo "✅ Endpoint progression accessible\n";
        echo "Réponse (200 premiers caractères): " . substr($progressionResult['response'], 0, 200) . "...\n";
    }
}

// Test 5: Vérifier les cours disponibles
echo "\n5. Test des cours disponibles...\n";
$coursResult = makeRequest($baseUrl . '/cours', 'GET', null, $authHeaders);
echo "Cours Status: " . $coursResult['http_code'] . "\n";

if ($coursResult['http_code'] === 200) {
    $coursData = json_decode($coursResult['response'], true);
    $courses = $coursData['hydra:member'] ?? $coursData ?? [];
    echo "Nombre de cours trouvés: " . count($courses) . "\n";
    
    if (count($courses) > 0) {
        echo "Premier cours: ID=" . ($courses[0]['id'] ?? 'N/A') . ", Titre=" . ($courses[0]['titre'] ?? 'N/A') . "\n";
    }
}

echo "\n=== Fin du diagnostic ===\n";
