# Test script to verify API authentication
$API_URL = "http://127.0.0.1:8000/api"

Write-Host "Testing API authentication..." -ForegroundColor Green
Write-Host "API URL: $API_URL" -ForegroundColor Yellow

# Test 1: Basic connectivity
Write-Host "`n1. Testing basic connectivity..." -ForegroundColor Cyan
try {
    $response = Invoke-WebRequest -Uri "$API_URL/test" -Method GET -UseBasicParsing
    Write-Host "Status: $($response.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($response.Content)" -ForegroundColor White
} catch {
    Write-Host "Status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Yellow
    Write-Host "Response: $($_.Exception.Response)" -ForegroundColor White
}

# Test 2: Login with valid credentials
Write-Host "`n2. Testing login with valid credentials..." -ForegroundColor Cyan
$loginData = @{
    email = "<EMAIL>"
    password = "password"
} | ConvertTo-<PERSON><PERSON>

try {
    $loginResponse = Invoke-WebRequest -Uri "$API_URL/login" -Method POST -Body $loginData -ContentType "application/json" -UseBasicParsing
    Write-Host "Login Status: $($loginResponse.StatusCode)" -ForegroundColor Green
    $loginResult = $loginResponse.Content | ConvertFrom-Json
    Write-Host "Login Response: $($loginResponse.Content)" -ForegroundColor White
    
    if ($loginResult.token) {
        $token = $loginResult.token
        Write-Host "Token obtained: $($token.Substring(0, 20))..." -ForegroundColor Green
        
        # Test 3: Access protected endpoint with token
        Write-Host "`n3. Testing protected endpoint with token..." -ForegroundColor Cyan
        $headers = @{
            "Authorization" = "Bearer $token"
            "Content-Type" = "application/json"
        }
        
        try {
            $protectedResponse = Invoke-WebRequest -Uri "$API_URL/user/me" -Method GET -Headers $headers -UseBasicParsing
            Write-Host "Protected endpoint status: $($protectedResponse.StatusCode)" -ForegroundColor Green
            Write-Host "Protected endpoint response: $($protectedResponse.Content)" -ForegroundColor White
        } catch {
            Write-Host "Protected endpoint error: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Test 4: Access progression endpoint
        Write-Host "`n4. Testing progression endpoint..." -ForegroundColor Cyan
        try {
            $progressionResponse = Invoke-WebRequest -Uri "$API_URL/progression/apprenant/41/cours/31" -Method GET -Headers $headers -UseBasicParsing
            Write-Host "Progression status: $($progressionResponse.StatusCode)" -ForegroundColor Green
            Write-Host "Progression response: $($progressionResponse.Content)" -ForegroundColor White
        } catch {
            Write-Host "Progression error status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Yellow
            Write-Host "Progression error: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Test 5: Access notification endpoint
        Write-Host "`n5. Testing notification endpoint..." -ForegroundColor Cyan
        try {
            $notificationResponse = Invoke-WebRequest -Uri "$API_URL/notification" -Method GET -Headers $headers -UseBasicParsing
            Write-Host "Notification status: $($notificationResponse.StatusCode)" -ForegroundColor Green
            Write-Host "Notification response: $($notificationResponse.Content)" -ForegroundColor White
        } catch {
            Write-Host "Notification error status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Yellow
            Write-Host "Notification error: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "Login error status: $($_.Exception.Response.StatusCode.value__)" -ForegroundColor Red
    Write-Host "Login error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nTest completed!" -ForegroundColor Green
