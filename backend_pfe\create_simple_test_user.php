<?php

// Script simple pour créer un utilisateur de test
$host = '127.0.0.1';
$dbname = 'back-symfony';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connexion à la base de données réussie.\n";
    
    // Vérifier si l'utilisateur test existe déjà
    $stmt = $pdo->prepare("SELECT id FROM utilisateur WHERE email = ?");
    $stmt->execute(['<EMAIL>']);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        echo "Utilisateur test existe déjà avec l'ID: " . $existingUser['id'] . "\n";
        $userId = $existingUser['id'];
    } else {
        // Créer un nouvel utilisateur avec un mot de passe hashé pour "password"
        $hashedPassword = '$2y$13$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'; // hash de "password"
        
        $stmt = $pdo->prepare("
            INSERT INTO utilisateur (name, email, phone, password, role, roles, is_approved, discr) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'Test User',
            '<EMAIL>',
            '1234567890',
            $hashedPassword,
            'apprenant',
            '["ROLE_APPRENANT"]',
            1,
            'apprenant'
        ]);
        
        $userId = $pdo->lastInsertId();
        echo "Utilisateur test créé avec l'ID: $userId\n";
    }
    
    echo "Email: <EMAIL>\n";
    echo "Mot de passe: password\n";
    
    // Test de connexion
    echo "\nTest de connexion...\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://127.0.0.1:8000/api/login');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode(['email' => '<EMAIL>', 'password' => 'password']));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Status: $httpCode\n";
    echo "Réponse: $response\n";
    
    if ($httpCode === 200) {
        $data = json_decode($response, true);
        if (isset($data['token'])) {
            echo "✅ Connexion réussie! Token obtenu.\n";
            $token = $data['token'];
            
            // Test des endpoints problématiques
            echo "\n=== Test des endpoints problématiques ===\n";
            
            // 1. Test notification
            echo "\n1. Test notification...\n";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, 'https://127.0.0.1:8000/api/notification');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                "Authorization: Bearer $token"
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $notifResponse = curl_exec($ch);
            $notifHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $notifError = curl_error($ch);
            curl_close($ch);
            
            echo "Notification Status: $notifHttpCode\n";
            if ($notifError) {
                echo "❌ Erreur cURL (ERR_NETWORK_IO_SUSPENDED): $notifError\n";
            } else {
                echo "✅ Endpoint notification accessible\n";
                echo "Réponse: " . substr($notifResponse, 0, 200) . "...\n";
            }
            
            // 2. Test progression
            echo "\n2. Test progression...\n";
            $apprenantId = $userId;
            $coursId = 31;
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "https://127.0.0.1:8000/api/progression/apprenant/$apprenantId/cours/$coursId");
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                "Authorization: Bearer $token"
            ]);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            
            $progResponse = curl_exec($ch);
            $progHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $progError = curl_error($ch);
            curl_close($ch);
            
            echo "Progression Status: $progHttpCode\n";
            if ($progError) {
                echo "❌ Erreur cURL: $progError\n";
            } else {
                if ($progHttpCode === 404) {
                    echo "⚠️  404 - Progression non trouvée (problème identifié)\n";
                } else {
                    echo "✅ Endpoint progression accessible\n";
                }
                echo "Réponse: " . substr($progResponse, 0, 300) . "...\n";
            }
            
        } else {
            echo "❌ Token non reçu dans la réponse\n";
        }
    } else {
        echo "❌ Échec de la connexion\n";
    }
    
} catch (PDOException $e) {
    echo "Erreur de connexion à la base de données: " . $e->getMessage() . "\n";
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
