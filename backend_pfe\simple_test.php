<?php

// Test simple des endpoints API
echo "=== Test simple des endpoints API ===\n\n";

// Configuration
$baseUrl = 'https://127.0.0.1:8000/api';

// Fonction pour faire des requêtes HTTP
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_VERBOSE, false);
    
    if ($method === 'POST') {
        curl_setopt($ch, CURLOPT_POST, true);
        if ($data) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
    }
    
    $defaultHeaders = ['Content-Type: application/json'];
    curl_setopt($ch, CURLOPT_HTTPHEADER, array_merge($defaultHeaders, $headers));
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'response' => $response,
        'http_code' => $httpCode,
        'error' => $error
    ];
}

// Test avec différents utilisateurs
$users = [
    ['email' => '<EMAIL>', 'password' => 'lyna123'],
    ['email' => '<EMAIL>', 'password' => 'tansim123'],
    ['email' => '<EMAIL>', 'password' => 'admin123']
];

foreach ($users as $user) {
    echo "Test avec: " . $user['email'] . "\n";
    
    $loginResult = makeRequest($baseUrl . '/login', 'POST', $user);
    
    echo "Status: " . $loginResult['http_code'] . "\n";
    echo "Réponse: " . $loginResult['response'] . "\n";
    
    if ($loginResult['http_code'] === 200) {
        $loginData = json_decode($loginResult['response'], true);
        $token = $loginData['token'] ?? null;
        
        if ($token) {
            echo "✅ Connexion réussie!\n";
            $authHeaders = ["Authorization: Bearer $token"];
            
            // Test notification
            echo "\nTest notification...\n";
            $notifResult = makeRequest($baseUrl . '/notification', 'GET', null, $authHeaders);
            echo "Notification Status: " . $notifResult['http_code'] . "\n";
            if ($notifResult['error']) {
                echo "Erreur: " . $notifResult['error'] . "\n";
            } else {
                echo "Réponse: " . substr($notifResult['response'], 0, 100) . "...\n";
            }
            
            // Test progression
            echo "\nTest progression...\n";
            $apprenantId = 41; // ID de lyna
            $coursId = 31;
            
            $progResult = makeRequest(
                $baseUrl . "/progression/apprenant/$apprenantId/cours/$coursId", 
                'GET', 
                null, 
                $authHeaders
            );
            echo "Progression Status: " . $progResult['http_code'] . "\n";
            if ($progResult['error']) {
                echo "Erreur: " . $progResult['error'] . "\n";
            } else {
                echo "Réponse: " . substr($progResult['response'], 0, 200) . "...\n";
            }
            
            break; // Arrêter après le premier utilisateur qui fonctionne
        }
    }
    
    echo "\n" . str_repeat("-", 50) . "\n\n";
}

echo "=== Fin des tests ===\n";
